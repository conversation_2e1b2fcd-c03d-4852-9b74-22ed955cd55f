/* survey.css - Final version with optimized results page */

/* --- Variables and Root Styles --- */
:root {
  --primary-color: #004a99;
  --secondary-color: #003366;
  --light-background: #e0f0ff;
  --font-family: 'Roboto', sans-serif;
  --text-color: #333;
  --background-color: #ffffff;
  --link-hover-color: #007bff;
}

html {
  scroll-behavior: smooth;
}

/* --- Section Instruction Styles --- */
.section-instruction {
  font-weight: bold;
  color: var(--primary-color);
  font-size: 1.1rem;
  margin-bottom: 20px;
  text-align: left;
  line-height: 1.5;
}

/* --- Container and Layout Styles --- */
.container {
  max-width: 800px;
  margin: 40px auto;
  background-color: #ffffff;
  padding: 30px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  overflow: hidden;
}

/* --- Header Styles --- */
.header-content {
  display: flex;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 25px 35px;
  align-items: center;
}

.logo-link {
  display: flex;
  align-items: center;
  position: absolute;
  left: 30px;
}

.logo {
  height: 40px;
  width: auto;
}

/* --- User Menu and Logout Button --- */
.user-menu {
  display: flex;
  align-items: center;
  position: absolute;
  right: 30px;
  gap: 10px;
}

.user-code {
  font-size: 14px;
  color: #666;
  background-color: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
  margin-right: 5px;
}

#logoutButton {
  background-color: #004a99;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#logoutButton:hover {
  background-color: #003366;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

#logoutButton:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

#logoutButton::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 6px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M16 17v-3H9v-4h7V7l5 5-5 5M14 2a2 2 0 012 2v2h-2V4H5v16h9v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h9z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
}

/* --- Survey Header and Progress Bar --- */
.survey-header {
  background: linear-gradient(135deg, #004a99 0%, #003366 100%);
  padding: 25px 30px;
  border-radius: 10px 10px 0 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.survey-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  border-radius: 50%;
}

.survey-header h2 {
  color: white;
  margin: 0 0 5px 0;
  font-size: 1.8rem;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.progress-container {
  margin: 15px 0 5px 0;
}

#progressBar {
  height: 8px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.2);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  width: 100%;
}

#progressFill {
  height: 100%;
  border-radius: 4px;
  background: linear-gradient(90deg, #ffffff 0%, #e0f0ff 100%);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: width 0.5s ease;
  width: 0;
}

#progressText {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  margin-top: 8px;
  text-align: right;
}


.fill-blanks-container {
  line-height: 2.2; /* Increased line height for better spacing around inputs */
  margin-top: 10px;
  color: var(--text-color); /* Ensure text color is consistent */
}

.fill-blanks-text {
  margin-right: 5px; /* Space after text part */
  margin-left: 5px;  /* Space before text part */
}

.fill-blanks-input {
  /* --- Key changes for size and alignment --- */
  display: inline-block; /* Allow it to flow with text */
  width: 150px; /* Set a specific width (adjust as needed) */
  max-width: 200px;
  vertical-align: baseline; /* Align input baseline with text */
  /* --- End Key changes --- */

  padding: 5px 8px;
  margin: 0 5px; /* Space around input */
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1em; /* Match surrounding text */
  font-family: inherit; /* Ensure consistent font */
  box-sizing: border-box; /* Include padding/border in width */
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.fill-blanks-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 74, 153, 0.2);
  outline: none;
}

/* Style for unanswered fill-blanks container */
.fill-blanks-container.unanswered {
   border: 1px solid #ff4444; /* Add border to the container */
   padding: 10px;
   border-radius: 4px;
   background-color: rgba(255, 68, 68, 0.05);
   display: block; /* Make it block to contain the border */
}

/* Ensure individual invalid blank inputs get red border */
.fill-blanks-input.invalid-input {
    border-color: #d9534f !important; /* Use !important if needed */
}

/* Ensure readonly inputs look disabled */
.fill-blanks-input[readonly] {
    background-color: #eee;
    cursor: not-allowed;
}

/* --- Typography --- */
h1,
h2 {
  color: var(--primary-color);
  margin: 0;
}

.section h2,
h2.section-title {
  color: #004a99;
  font-size: 1.5rem;
  font-weight: 600;
  padding-bottom: 12px;
  border-bottom: 2px solid #e0e0e0;
  margin-bottom: 20px;
  position: relative;
}

.section h2::after,
h2.section-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 80px;
  height: 2px;
  background-color: #004a99;
}

/* --- Section Styles --- */
.section {
  background: white;
  padding: 1rem;
}

/* --- Question Styles --- */
.question {
  margin-bottom: 25px;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.question:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.question p {
  font-weight: normal;
  color: #333;
  margin-bottom: 15px;
}

.question p strong {
  font-weight: bold; /* Ensure <strong> tags within the paragraph are still bold */
}
.question-group {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
}

.question-group-title {
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
}

/* --- Radio Button Styles --- */
.radio-group {
  margin: 10px 0;
  display: flex;
  flex-direction: column;
}

.radio-option {
  margin: 5px 0;
}

.radio-option label {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  line-height: 1.4;
}

.radio-option:focus-within {
  outline: 2px solid var(--primary-color);
  border-radius: 4px;
}

.radio-option input[type='radio'] {
  margin-right: 10px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  outline: none;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
  position: absolute;
  left: -9999px;
}

.radio-option input[type='radio']:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.radio-option input[type='radio']:focus + .radio-checkmark {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.radio-option input[type='radio']:disabled + .radio-checkmark {
  opacity: 0.7;
  cursor: not-allowed;
}

.radio-checkmark {
  display: inline-block;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 2px solid var(--primary-color);
  margin-right: 12px;
  margin-top: 2px;
  position: relative;
  flex-shrink: 0;
}

.radio-option input[type='radio']:checked + .radio-checkmark::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--primary-color);
}

/* Styles for the email input group at the end of the survey */
.email-input-group {
  display: flex;
  align-items: center; /* Vertically align input and button */
  gap: 10px; /* Space between input and button */
  margin-top: 15px; /* Space above the input group */
}

.email-input-group input[type="email"] {
  flex-grow: 1; /* Make the input field take available width */
  padding: 10px; /* Standard padding */
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1em; /* Match other inputs */
  box-sizing: border-box;
  /* Ensure no conflicting margin-top from general input styles */
  margin-top: 0;
}

.email-input-group input[type="email"]:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 74, 153, 0.2);
  outline: none;
}

.email-input-group button {
  /* Assuming .btn .btn-primary styles are applied by JavaScript.
     This ensures the button's padding makes it align well with the input height. */
  padding: 10px 15px;
  white-space: nowrap; /* Prevent button text from wrapping */
  flex-shrink: 0; /* Prevent button from shrinking if space is tight */
  /* Ensure consistent height with the input field */
  height: auto; /* Adjust if input has a fixed height */
  line-height: 1.4; /* Adjust for vertical text centering if padding makes it tall */
}

/* Ensure .final-inputs (used as the main container for this email section)
   has appropriate styling for its paragraph content. */
.final-inputs p {
  margin-bottom: 15px; /* Ensure space between text and the email input group */
  line-height: 1.6;
  font-weight: normal; /* Override if it inherits strong from parent */
}

.final-inputs p strong {
    font-weight: bold; /* Keep the strong tag bold */
}

/* --- Scale Rating Styles --- */
.rating-scale {
  display: flex;
  justify-content: space-between;
  margin: 15px 0;
  gap: 5px;
}

.scale-button {
  position: relative;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: linear-gradient(145deg, #f0f0f0, #e6e6e6);
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: bold;
}

.scale-button:hover {
  transform: translateY(-2px);
  box-shadow: 3px 3px 8px rgba(0, 0, 0, 0.15);
}

.scale-label input[type='radio'] {
  position: absolute;
  opacity: 0;
  height: 0;
  width: 0;
}

.scale-label input[type='radio']:checked + .scale-button {
  background: linear-gradient(
    145deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  transform: translateY(-3px);
  box-shadow: 3px 3px 10px rgba(0, 74, 153, 0.3);
}

.scale-labels {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 8px;
  padding: 0 5px;
  font-size: 0.9em;
  color: #666;
}

.scale-labels span:first-child {
  margin-left: 2px;
}

.scale-labels span:last-child {
  margin-right: 2px;
}

/* --- Input Field Styles --- */
input[type='text'],
input[type='number'],
input[type='date'],
select,
textarea {
  width: 100%;
  padding: 10px;
  margin-top: 5px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 16px;
}

input[type='text']:focus,
input[type='number']:focus,
textarea:focus,
select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 74, 153, 0.2);
  outline: none;
  transition: all 0.2s ease;
}

select:disabled {
  opacity: 1;
  background-image: none;
  padding-right: 10px;
  background-color: #f0f0f0;
  cursor: not-allowed;
}

select {
  appearance: none;
  background-image: url('data:image/svg+xml;utf8,<svg fill="black" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/><path d="M0 0h24v24H0z" fill="none"/></svg>');
  background-repeat: no-repeat;
  background-position: 98% 50%;
}

/* --- Conditional Field Styles --- */
.conditional-field {
  margin: 15px 0;
  padding: 10px;
  border-left: 3px solid var(--primary-color);
  background: #f8f9fa;
}

/* --- Navigation Buttons --- */
.navigation-buttons {
  display: flex;
  gap: 10px;
  justify-content: space-between;
  padding: 15px;
  background-color: #fff;
  border-top: 1px solid #eee;
}

#prevButton {
  order: 1;
  margin-right: auto;
}

#saveProgressButton {
  order: 2;
  margin: 0 auto;
}

#nextButton {
  order: 3;
  margin-left: auto;
}

.datenschutz-section .navigation-buttons {
  justify-content: flex-end;
  padding: 20px 0 0 0;
  border-top: none;
}

#nextButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  background-color: #6c757d;
  border-color: #6c757d;
}

#nextButton:disabled:hover {
  background-color: #6c757d;
}

/* --- Validation Styles --- */
.unanswered {
  position: relative;
  border: 2px solid #ff4444 !important;
  border-radius: 4px;
  background-color: rgba(255, 68, 68, 0.05);
  animation: pulse-error 2s infinite;
}

.unanswered-message {
  position: absolute;
  bottom: -20px;
  left: 10px;
  color: #dc3545;
  font-size: 0.85em;
  background: white;
  padding: 2px 5px;
  border-radius: 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

/* --- Datenschutzerklärung Styles --- */
.datenschutz-section {
  margin-bottom: 40px;
}

.datenschutz-content {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.datenschutz-inputs .question {
  background: none;
  box-shadow: none;
  padding: 0;
  margin-bottom: 1rem;
}

.agreement-questions .agreement label {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 0.5rem;
}

.agreement-questions .agreement input[type='checkbox'] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid var(--primary-color);
  border-radius: 4px;
  margin-right: 10px;
  margin-top: -50px;
  outline: none;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
}

.agreement-questions .agreement input[type='checkbox']:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.agreement-questions .agreement input[type='checkbox']:checked::before {
  content: '✓';
  display: block;
  color: white;
  font-size: 14px;
  text-align: center;
  line-height: 18px;
}

.final-inputs {
  border-radius: 8px;
  margin-top: 1.5rem;
}

/* --- Chart and Results Section Styles --- */
.chart-container {
  padding: 0.5rem 1rem 0.5rem;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(
    to right,
    var(--primary-color),
    var(--secondary-color)
  );
  border-radius: 8px 8px 0 0;
}

.chart-title {
  text-align: center;
  font-weight: 600;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  color: var(--primary-color);
}

/* Hide the redundant user code in chart */
.chart-container canvas + div[style*='text-align: right'],
canvas + div.user-code-display {
  display: none !important;
}

/* Override any plugin-generated user code display */
[id*='chartjs-tooltip'] .user-code,
.chart-js-tooltip .user-code,
canvas + span.user-code {
  display: none !important;
}

/* Results Section Styles */
.results-section {
  padding: 1.5rem;
  background: #fff;
  border-radius: 10px;
}

.results-section h3,
.results-section h4 {
  color: var(--primary-color);
  border-bottom: 2px solid #eaeaea;
  font-weight: 600;
}

.results-section p {
  margin-bottom: 1.2rem;
  line-height: 1.6;
}

/* Create consistent styling for information boxes */
.info-box,
#descriptionBox1,
#competencyDescription,
#resultsDescriptionBox {
  background-color: #f8f9fa;
  border-left: 4px solid var(--primary-color);
  padding: 1.25rem;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* Highlight key information */
.results-section strong {
  color: var(--primary-color);
  font-weight: 600;
}

/* Better score display */
.score-display {
  font-size: 1.2rem;
  background: #e0f0ff;
  padding: 0.8rem 1.2rem;
  border-radius: 6px;
  display: inline-block;
}

/* Consistent button styling */
.results-section button,
.chart-download-button,
.export-button,
#downloadChart,
#exportPdfBtn,
#submitT1OpenEndedResponse,
#submitT2OpenEndedResponse,
#submitT3OpenEndedResponse,
#submitT3AdditionalResponse,
#submitT3EmailResponse {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-block;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.results-section button:hover,
.chart-download-button:hover,
.export-button:hover,
#downloadChart:hover,
#exportPdfBtn:hover,
#submitT1OpenEndedResponse:hover,
#submitT2OpenEndedResponse:hover,
#submitT3OpenEndedResponse:hover,
#submitT3AdditionalResponse:hover,
#submitT3EmailResponse:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Button container for better alignment */
.button-container {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

/* Better section spacing */
.results-section > div,
.results-section > p,
.results-section > h3,
.results-section > h4 {
  margin-bottom: 1.5rem;
}

/* Improve feedback textarea */
.results-section textarea {
  width: 100%;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  min-height: 120px;
  font-family: inherit;
  margin: 10px bottom;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.validation-error-message {
  display: block; /* Ensure it takes its own line */
  color: #d9534f; /* Bootstrap's danger color (or choose your own red) */
  font-size: 0.85em; /* Slightly smaller font */
  margin-top: 5px; /* Space above the message */
  margin-left: 5px; /* Indent slightly if needed */
  font-weight: bold;
}

/* Optional: Style for the input itself when invalid */
input.invalid-input,
select.invalid-input,
textarea.invalid-input {
  border-color: #d9534f !important; /* Force red border */
  box-shadow: 0 0 0 0.2rem rgba(217, 83, 79, 0.25); /* Optional: Add a glow */
}

.results-section textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 74, 153, 0.1);
  outline: none;
}

/* Add a "complete" message when textarea is disabled */
.results-section textarea:disabled {
  background-color: #f9f9f9;
  cursor: not-allowed;
  border-color: #ddd;
}

.results-section textarea:disabled + button:disabled::after {
  content: '✓ Gespeichert';
  display: inline-block;
  margin-left: 8px;
  font-size: 0.9rem;
  color: #4caf50;
}

/* Attention box for important notices */
.attention-box {
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 5px;
  color: #856404;
  display: flex;
  align-items: center;
  font-size: 16px;
}

/* --- Competency Areas and ILIAS Links Styling --- */
.competency-areas-overview {
  margin: 2rem 0;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.competency-areas-overview h3 {
  color: var(--primary-color);
  margin-bottom: 15px;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 8px;
}

.competency-area {
  margin-bottom: 20px;
  padding: 15px;
  background-color: white;
  border-left: 4px solid var(--primary-color);
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.competency-area h4 {
  color: var(--primary-color);
  margin-top: 0;
  margin-bottom: 10px;
  font-weight: 600;
}

.competency-area p {
  margin: 0;
  line-height: 1.5;
  color: #333;
}

/* --- Instruction for New Competency Questions --- */
.competency-task-instruction {
  font-weight: bold;
  color: var(--primary-color);
  margin-top: 2rem; /* Add space before these tasks */
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px dashed #ccc;
}

/* css/survey.css */

/* --- Checkbox Group Styling --- */
.checkbox-group {
  margin: 10px 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.checkbox-option label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 1rem;
  color: var(--text-color);
}

.checkbox-option input[type='checkbox'] {
  position: absolute; /* Hide the actual checkbox */
  opacity: 0;
  height: 0;
  width: 0;
}

/* Create a custom visual element for the checkbox */
.checkbox-label::before {
  content: '';
  display: inline-block;
  width: 18px;
  height: 18px;
  border: 2px solid var(--primary-color);
  border-radius: 50%; /* Make it a circle */
  margin-right: 12px;
  transition: background-color 0.2s, border-color 0.2s;
  flex-shrink: 0;
  line-height: 16px; /* Center the checkmark vertically */
  text-align: center; /* Center the checkmark horizontally */
  font-weight: bold;
  color: white;
}

/* Style when checked */
.checkbox-option input[type='checkbox']:checked + .checkbox-label::before {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  content: '✓'; /* Add checkmark content */
}

/* Focus style */
.checkbox-option input[type='checkbox']:focus + .checkbox-label::before {
  box-shadow: 0 0 0 3px rgba(0, 74, 153, 0.25);
}

/* Disabled style */
.checkbox-option input[type='checkbox']:disabled + .checkbox-label::before {
  border-color: #ccc;
  background-color: #f0f0f0;
  cursor: not-allowed;
}

.checkbox-option input[type='checkbox']:disabled:checked + .checkbox-label::before {
  background-color: #ccc;
  color: #999;
}

/* Style for unanswered checkbox group */
.checkbox-group.unanswered {
  border: 1px solid #ff4444;
  padding: 10px;
  border-radius: 4px;
  background-color: rgba(255, 68, 68, 0.05);
}
/* --- Textarea Styling (Ensure it exists or refine) --- */
textarea {
  width: 100%;
  padding: 10px;
  margin-top: 5px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 16px;
  min-height: 80px; /* Set a minimum height */
  resize: vertical; /* Allow vertical resizing */
  font-family: inherit; /* Use the main font */
}

/* --- Refine Unanswered Styling for Question Div --- */
/* Make the pulse animation less aggressive */
@keyframes pulse-error-gentle {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.3);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(255, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 68, 68, 0);
  }
}

/* Apply gentle pulse to non-group unanswered questions */
.question.unanswered:not(:has(.checkbox-group)):not(:has(.fill-blanks-container)) {
   animation: pulse-error-gentle 1.5s infinite;
}

/* Ensure shake animation is defined if not already */
@keyframes shake {
  10%, 90% { transform: translate3d(-1px, 0, 0); }
  20%, 80% { transform: translate3d(2px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
  40%, 60% { transform: translate3d(4px, 0, 0); }
}

/* Apply shake animation on validation failure (JS adds/removes this) */
.question.shake-animation,
.checkbox-group.shake-animation,
.fill-blanks-container.shake-animation {
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}
/* Improved ILIAS links styling */
.ilias-links {
  border-radius: 8px;
  padding: 1em, 1.5em, 1em, 1.5em;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.ilias-links h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--primary-color);
  border-bottom: none;
  padding-bottom: 0.5rem;
}

.ilias-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  grid-gap: 1rem;
}

.ilias-links li {
  margin-bottom: 0;
}

.ilias-links a {
  display: block;
  padding: 1rem;
  background-color: white;
  border-radius: 6px;
  color: var(--primary-color);
  text-decoration: none;
  border: 1px solid #ddd;
  transition: all 0.2s ease;
}

.ilias-links a:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* New course link box styling */
.course-link-box {
  display: block;
  padding: 1.5rem;
  background-color: var(--primary-color);
  border-radius: 8px;
  color: white;
  text-decoration: none;
  border: none;
  transition: all 0.3s ease;
  text-align: center;
  margin: 2rem 0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.course-link-box:hover {
  background-color: #003366;
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.course-link-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.course-link-content h3 {
  margin: 0 0 0.5rem 0;
  color: inherit;
  font-size: 1.3rem;
}



.click-hint {
  font-size: 1rem;
  opacity: 0.9;
  margin-top: 0.5rem;
  font-style: italic;
  color: inherit;
}

/* --- Course Links Styles --- */
.course-links {
  margin-top: 2rem;
}

.course-links ul {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  list-style: none;
  padding: 0;
}

.course-links li a {
  display: block;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  transition: transform 0.2s ease;
  color: var(--primary-color);
  text-decoration: none;
}

.course-links li a:hover {
  transform: translateY(-2px);
  background: var(--primary-color);
  color: white;
  text-decoration: none;
}

/* --- Animations --- */
@keyframes shake {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  50% {
    transform: translateX(5px);
  }
  75% {
    transform: translateX(-5px);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes pulse-error {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 68, 68, 0);
  }
}

/* --- Responsive Adjustments --- */
@media (max-width: 768px) {
  .container {
    margin: 20px;
    padding: 20px;
  }

  .navigation-buttons {
    flex-direction: column;
  }

  .user-menu {
    position: relative;
    right: auto;
    justify-content: flex-end;
    margin-bottom: 10px;
  }

  .header-content {
    flex-direction: column;
  }

  .ilias-links ul,
  .course-links ul {
    grid-template-columns: 1fr;
  }

  .button-container {
    flex-direction: column;
  }

  .button-container button {
    width: 100%;
    margin: 0.5rem 0;
  }

  .results-section {
    padding: 1.5rem;
  }
}

/* --- Inline Inputs Styling (for Q4_7) --- */
.inline-inputs-container {
  display: flex;
  flex-direction: column;
  gap: 1rem; /* Space between each row */
}

.inline-input-group {
  display: flex;
  align-items: center;
  gap: 10px; /* Space between label and input */
}

.inline-input-group label {
  font-weight: bold;
  color: var(--text-color);
  flex-basis: 120px; /* Give all labels a consistent width */
  flex-shrink: 0; /* Prevent labels from shrinking */
  text-align: right; /* Align text to the right for a clean look */
  padding-right: 10px;
}

.inline-input-group input[type='text'] {
  flex-grow: 1; /* Allow input to take up remaining space */
  margin-top: 0; /* Override default margin from other inputs */
}


/* --- Instruction for New Competency Questions --- */
.competency-task-instruction {
  font-weight: bold;
  color: var(--primary-color);
  margin-top: 2rem; /* Add space before these tasks */
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px dashed #ccc;
}