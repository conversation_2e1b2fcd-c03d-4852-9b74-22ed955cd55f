let users = [];
let questionIds = []; // This will be set from the server
let currentPage = 1;
const usersPerPage = 50;

// This will store the mapping from codebook variable name to its prompt and data access path
let codebookColumnMap = {};

// js/dashboard.js

// js/dashboard.js

function initializeCodebookMap() {
  codebookColumnMap = {
    // Pre-Survey
    Code: { prompt: "Code", source: "userCode", type: "data" },
    Attempt: {
      prompt: "Current or highest survey attempt number",
      source: "attemptNumber",
      type: "data",
    },
    Forschungsteilnahme: {
      prompt:
        "Nehmen Sie im Rahmen des Forschungsprojektes an der Befragung teil?",
      source: "preSurveyResponses.q-2_0",
      type: "data",
    },
    Gruppe: {
      prompt: "In welcher Gruppe befinden Sie sich?",
      type: "calculated",
    },
    "Datenschutz accepted?": {
      prompt:
        "Indicates if consent was given or implied by survey start/completion",
      type: "calculated",
    },
    Unterschrift: {
      prompt: "User's signature, their code",
      source: "unterschrift",
      type: "data",
    },
    // Section 0: Persönliche Angaben
    Gender: {
      prompt: "Welches Geschlecht haben Sie?",
      source: "initialResponses.q0_0",
      type: "data",
    },
    "Birth Year": {
      prompt: "In welchem Jahr sind Sie geboren? Füge das Jahr als Zahl ein",
      source: "initialResponses.q0_1",
      type: "data",
    },
    "Studieren Sie Lehramt?": {
      prompt: "Studieren Sie Lehramt?",
      source: "initialResponses.q0_2",
      type: "data",
    },
    Lehramt: {
      prompt: "Welches Lehramt studieren Sie?",
      source: "initialResponses.q0_6",
      type: "data",
    },
    Fächer: {
      prompt: "Welche Fächer studieren Sie aktuell in Ihrem Lehramtsstudium?",
      source: "initialResponses.q0_7",
      type: "data",
    },
    Studiengang: {
      prompt: "Was studieren Sie?",
      source: "initialResponses.q0_8",
      type: "data",
    },
    Semester: {
      prompt:
        "Im welchem Fachsemester befinden Sie sich aktuell? (Bitte geben Sie das höchste Fachsemester an.)",
      source: "initialResponses.q0_3",
      type: "data",
    },
    "Abitur Grade": {
      prompt:
        "Mit welchem Durchschnitt hast du dein Abitur abgeschlossen? Bitte gebe hier den exakten Wert an, z.B. 2.3.",
      source: "initialResponses.q0_4",
      type: "data",
    },

    // Open-Ended Questions
    "Strategy (T1).A": {
      prompt:
        "(Group A - T1) Schauen Sie sich nun Ihre Ergebnisse an, benennen Sie unten im Feld ein oder zwei Kompetenzbereiche, in denen Sie Mikrofortbildungen absolvieren möchten und begründen Sie Ihre Entscheidung.",
      oerKey: "t1_strategy",
      group: "A",
      type: "openEnded",
    },
    "Strategy (T1).B": {
      prompt:
        "(Group B - T1) Benennen Sie unten im Feld ein oder zwei Kompetenzbereiche, in denen Sie Mikrofortbildungen absolvieren möchten und begründen Sie Ihre Entscheidung.",
      oerKey: "t1_strategy",
      group: "B",
      type: "openEnded",
    },
    "Number of Courses (T2)": {
      prompt: "Wie viele Kurse haben Sie auf Ilias absolviert?",
      qId: "q1_16",
      attemptKey: "updatedResponses",
      type: "question",
    },
    // --- UPDATED SECTION START ---
    "Courses (T2).A": {
      prompt:
        "(Group A - T2) Welche Kurse bzw. Fortbildungen haben Sie in den letzten Wochen auf Ilias absolviert?",
      qId: "q1_17",
      attemptKey: "updatedResponses",
      group: "A",
      type: "question",
    },
    "Courses (T2).B": {
      prompt:
        "(Group B - T2) Welche Kurse bzw. Fortbildungen haben Sie in den letzten Wochen auf Ilias absolviert?",
      qId: "q1_17",
      attemptKey: "updatedResponses",
      group: "B",
      type: "question",
    },
    "Course Feedback (T2).A": {
      prompt:
        "(Group A - T2) Wie fandest Sie die absolvierten Kurse in ILIAS in Bezug auf Inhalt und Struktur? Was haben Sie mitgenommen? Was war hilfreich für Sie?",
      qId: "q1_18",
      attemptKey: "updatedResponses",
      group: "A",
      type: "question",
    },
    "Course Feedback (T2).B": {
      prompt:
        "(Group B - T2) Wie fandest Sie die absolvierten Kurse in ILIAS in Bezug auf Inhalt und Struktur? Was haben Sie mitgenommen? Was war hilfreich für Sie?",
      qId: "q1_18",
      attemptKey: "updatedResponses",
      group: "B",
      type: "question",
    },
    // --- UPDATED SECTION END ---
    "Reflection (T2).A": {
      prompt:
        "(Group A - T2) Wie haben sich Ihre Kompetenzüberzeugungen nun verändert? Beschreiben Sie, was Sie im Diagramm sehen und welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen. Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche.",
      oerKey: "t2_reflection",
      group: "A",
      type: "openEnded",
    },
    "Reflection (T2).B": {
      prompt:
        "(Group B - T2) Was glauben Sie nun nach Absolvierung der Kurse, wie haben sich Ihre Kompetenzüberzeugungen verändert? Beschreiben Sie unten im Feld, welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen.",
      oerKey: "t2_reflection",
      group: "B",
      type: "openEnded",
    },
    "Reflection (T2).C": {
      prompt:
        "(Group C - T2) Wie haben sich Ihre Kompetenzüberzeugungen nun verändert? Beschreiben Sie, was Sie im Diagramm sehen und welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen. Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche.",
      oerKey: "t2_reflection",
      group: "C",
      type: "openEnded",
    },
    "Reflection (T2).D": {
      prompt:
        "(Group D - T2) Was glauben Sie, haben sich Ihre Kompetenzüberzeugungen im Vergleich zur ersten Befragung verändert? Beschreiben Sie unten im Feld, welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen.",
      oerKey: "t2_reflection",
      group: "D",
      type: "openEnded",
    },
    "Reflection (T3).A": {
      prompt:
        "(Group A - T3) Wie haben sich Ihre Kompetenzüberzeugungen nun verändert? Beschreiben Sie, was Sie im Diagramm sehen und welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen. Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche.",
      oerKey: "t3_reflection",
      group: "A",
      type: "openEnded",
    },
    "Reflection (T3).B": {
      prompt:
        "(Group B - T3, Step 1) Was glauben Sie nun nach Abschluss des Projektes, wie haben sich Ihre Kompetenzüberzeugungen verändert? Beschreiben Sie unten im Feld, welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen.",
      oerKey: "t3_reflection",
      group: "B",
      type: "openEnded",
    },
    "Reflection (T3).C": {
      prompt:
        "(Group C - T3) Wie haben sich Ihre Kompetenzüberzeugungen nun verändert? Beschreiben Sie, was Sie im Diagramm sehen und welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen. Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche.",
      oerKey: "t3_reflection",
      group: "C",
      type: "openEnded",
    },
    "Reflection (T3).D": {
      prompt:
        "(Group D - T3, Step 1) Was glauben Sie nun nach Abschluss des Projektes, wie haben sich Ihre Kompetenzüberzeugungen verändert? Beschreiben Sie unten im Feld, welche Schlüsse Sie aus Ihrer Lernerfahrung ziehen.",
      oerKey: "t3_reflection",
      group: "D",
      type: "openEnded",
    },
    "Additional Reflection (T3).B": {
      prompt:
        "(Group B - T3, Step 2) Abschließend würden wir gerne wissen, was Sie denken. Passen Ihre vorherigen Einschätzungen zu Ihrer Entwicklung zu den Ergebnissen aus der Grafik? Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche.",
      oerKey: "t3_additional_reflection",
      group: "B",
      type: "openEnded",
    },
    "Additional Reflection (T3).D": {
      prompt:
        "(Group D - T3, Step 2) Abschließend würden wir gerne wissen, was Sie denken. Passen Ihre vorherigen Einschätzungen zu Ihrer Entwicklung zu den Ergebnissen aus der Grafik? Beziehen Sie sich dabei bitte auf alle sechs Kompetenzbereiche.",
      oerKey: "t3_additional_reflection",
      group: "D",
      type: "openEnded",
    },
    "Email (T3).A": {
      prompt:
        "(Group A - T3)Sie sind nun am Ende des Forschungsprojektes angekommen. Wenn Sie Interesse an einem weiteren Austausch zu ihrer Lernerfahrung haben, tragen Sie im Folgenden bitte Ihre E-Mail Adresse ein und wir werden uns bei Ihnen melden.",
      oerKey: "t3_email",
      group: "A",
      type: "openEnded",
    },
    "Email (T3).B": {
      prompt:
        "(Group B - T3)Sie sind nun am Ende des Forschungsprojektes angekommen. Wenn Sie Interesse an einem weiteren Austausch zu ihrer Lernerfahrung haben, tragen Sie im Folgenden bitte Ihre E-Mail Adresse ein und wir werden uns bei Ihnen melden.",
      oerKey: "t3_email",
      group: "B",
      type: "openEnded",
    },

    // Timestamps
    "T1 Start Timestamp": {
      prompt: "Start timestamp for attempt 1",
      source: "timeStamps.t1.start",
      format: "date",
      type: "data",
    },
    "T1 End Timestamp": {
      prompt: "End timestamp for attempt 1",
      source: "timeStamps.t1.end",
      format: "date",
      type: "data",
    },
    "T1 Duration": {
      prompt: "Duration for attempt 1 (minutes)",
      type: "calculatedDuration",
      attempt: 1,
    },

    "T2 Start Timestamp": {
      prompt: "Start timestamp for attempt 2",
      source: "timeStamps.t2.start",
      format: "date",
      type: "data",
    },
    "T2 End Timestamp": {
      prompt: "End timestamp for attempt 2",
      source: "timeStamps.t2.end",
      format: "date",
      type: "data",
    },
    "T2 Duration": {
      prompt: "Duration for attempt 2 (minutes)",
      type: "calculatedDuration",
      attempt: 2,
    },

    "T3 Start Timestamp": {
      prompt: "Start timestamp for attempt 3",
      source: "timeStamps.t3.start",
      format: "date",
      type: "data",
    },
    "T3 End Timestamp": {
      prompt: "End timestamp for attempt 3",
      source: "timeStamps.t3.end",
      format: "date",
      type: "data",
    },
    "T3 Duration": {
      prompt: "Duration for attempt 3 (minutes)",
      type: "calculatedDuration",
      attempt: 3,
    },
  };

  // Dynamically add question columns from surveyData
  if (typeof surveyData !== "undefined" && Array.isArray(surveyData)) {
    surveyData.forEach((section) => {
      if (section.questions && Array.isArray(section.questions)) {
        section.questions.forEach((question) => {
          const qId = question.id;
          // Skip demographics, pre-survey, instruction types, and manually handled questions
          if (
            qId.startsWith("q0_") ||
            qId.startsWith("q-2_") ||
            question.type === "instruction" ||
            qId.endsWith("_intro") ||
            qId === "q1_16" ||
            qId === "q1_17" ||
            qId === "q1_18"
          )
            return;

          const questionText = question.text || qId;

          // Handle questions based on their attempt logic
          if (question.attempt) {
            question.attempt.forEach((att) => {
              codebookColumnMap[`${qId} (T${att})`] = {
                prompt: questionText,
                qId: qId,
                attemptKey:
                  att === 1
                    ? "initialResponses"
                    : att === 2
                    ? "updatedResponses"
                    : "followUpResponses",
                type: "question",
              };
            });
          } else {
            // Default for questions without specific attempt array (T1, T2, T3)
            codebookColumnMap[`${qId} (T1)`] = {
              prompt: questionText,
              qId: qId,
              attemptKey: "initialResponses",
              type: "question",
            };
            codebookColumnMap[`${qId} (T2)`] = {
              prompt: questionText,
              qId: qId,
              attemptKey: "updatedResponses",
              type: "question",
            };
            codebookColumnMap[`${qId} (T3)`] = {
              prompt: questionText,
              qId: qId,
              attemptKey: "followUpResponses",
              type: "question",
            };
          }
        });
      }
    });
  }
}

// Whitelist of question IDs that have T2 versions
// Only these questions have T2-specific text in the survey
const t2Whitelist = new Set(["q1_0", "q1_1", "q1_2", "q1_3", "q1_4", "q1_5"]);

// Corrected escapeHtml function
function escapeHtml(str) {
  if (!str) return "";
  return str
    .replace(/&/g, "&")
    .replace(/</g, "<")
    .replace(/>/g, ">")
    .replace(/"/g, '"')
    .replace(/'/g, "&#39;");
}

// Add a helper function to prevent errors when fields don't exist
function safeAccess(obj, path) {
  if (!obj) return "";
  // Ensure path is a string before splitting
  if (typeof path !== "string") return "";
  return path
    .split(".")
    .reduce(
      (o, key) => (o && o[key] !== undefined && o[key] !== null ? o[key] : ""),
      obj
    );
}

// Natural sort function for correctly sorting strings with numbers
function naturalSort(a, b) {
  // This regex is specifically tailored for the qX_Y format
  const aMatch = a.match(/^q(\d+)_(\d+)$/);
  const bMatch = b.match(/^q(\d+)_(\d+)$/);

  if (aMatch && bMatch) {
    // Compare the first number (section number)
    const aNum1 = parseInt(aMatch[1], 10);
    const bNum1 = parseInt(bMatch[1], 10);
    if (aNum1 !== bNum1) {
      return aNum1 - bNum1; // Sort by section number first (q1 vs q2)
    }

    // If section numbers are the same, compare the second number (question index)
    const aNum2 = parseInt(aMatch[2], 10);
    const bNum2 = parseInt(bMatch[2], 10);
    return aNum2 - bNum2; // Sort by question index within section (q1_1 vs q1_2 vs q1_10)
  }

  // Fallback to regular string comparison if the pattern doesn't match
  // (Shouldn't happen for valid qX_Y IDs, but good practice)
  return a.localeCompare(b);
}

function addTruncationStyles() {
  if (document.getElementById("dashboard-truncation-styles")) return;

  const style = document.createElement("style");
  style.id = "dashboard-truncation-styles";
  style.textContent = `
    .simple-cell {
      display: block;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    .expandable-cell {
      display: flex;
      align-items: flex-start;
      width: 100%;
      min-height: 1.5em;
      transition: all 0.3s ease;
      gap: 6px;
    }

    .expandable-cell.expanded {
      align-items: flex-start;
    }

    .cell-content {
      flex-grow: 1;
      transition: all 0.3s ease;
      word-wrap: break-word;
      overflow-wrap: break-word;
      min-width: 0; /* Important for flex items */
    }

    .cell-truncated-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: block;
    }

    .cell-full-text {
      white-space: normal;
      word-wrap: break-word;
      overflow-wrap: break-word;
      display: block;
      line-height: 1.4;
      max-height: 200px;
      overflow-y: auto;
      padding-right: 5px;
    }

    .cell-full {
      display: none;
    }

    .expand-toggle-btn {
      margin-left: 6px;
      background: none;
      border: none;
      cursor: pointer;
      font-size: 1em;
      padding: 2px 4px;
      color: #0d6efd; /* Bootstrap primary blue */
      line-height: 1;
      flex-shrink: 0;
      border-radius: 3px;
      transition: all 0.2s ease;
    }

    .expand-toggle-btn:hover {
      color: #0a58ca; /* Darker blue on hover */
      background-color: rgba(13, 110, 253, 0.1);
    }

    .expand-toggle-btn:focus {
      outline: 2px solid #0d6efd;
      outline-offset: 2px;
    }

    /* Additional styles for dashboard cells */
    #userTable th {
      min-width: 200px; /* Default minimum width to fit column titles */
      white-space: nowrap; /* Keep header text on a single line */
      overflow: hidden; /* Hide overflow */
      text-overflow: ellipsis; /* Add ellipsis for overflow */
      vertical-align: middle; /* Center text vertically */
      padding: 8px 4px; /* Add some padding */
      font-weight: bold; /* Make headers bold */
      max-width: 300px; /* Default maximum width */
    }

    /* Smaller width for scoring columns */
    #userTable th.score-column {
      min-width: 60px; /* Much smaller for single digit scores */
      max-width: 80px;
      font-size: 0.85em; /* Slightly smaller font for score headers */
    }

    #userTable td {
      min-width: 200px; /* Default match header min-width */
      max-width: 300px; /* Default match header max-width */
      position: relative;
      vertical-align: top; /* Align content to the top of the cell */
      padding: 8px 5px; /* Better padding for expanded content */
      /* Remove overflow hidden to allow expansion */
    }

    /* Smaller width for scoring cells */
    #userTable td.score-cell {
      min-width: 60px; /* Much smaller for single digit scores */
      max-width: 80px;
      text-align: center; /* Center the single digit numbers */
      padding: 8px 2px; /* Less horizontal padding */
    }

    /* For cells without expandable content, maintain truncation */
    #userTable td:not(:has(.expandable-cell)) {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    /* Ensure td content doesn't push the cell too wide by default */
    #userTable td > span:not(.cell-full) { /* Target direct span children that are not the full view */
        display: block; /* Or inline-block */
        max-width: 100%; /* Constrain to cell width */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Make the table horizontally scrollable */
    .table-responsive {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
    }

    /* Add click-outside functionality for expanded cells */
    .cell-full-active {
      display: block !important;
    }
  `;
  document.head.appendChild(style);

  // Add click handler to document to close expanded cells when clicking outside
  document.addEventListener("click", function (event) {
    const expandedCells = document.querySelectorAll(".cell-full-active");
    if (expandedCells.length > 0) {
      expandedCells.forEach((cell) => {
        // Check if click was outside the cell
        if (
          !cell.contains(event.target) &&
          !event.target.classList.contains("expand-toggle-btn")
        ) {
          cell.classList.remove("cell-full-active");
          // Also update the button icon
          const cellId = cell.id.replace("_full", "");
          const btn = document.querySelector(`button[onclick*="${cellId}"]`);
          if (btn) {
            btn.innerHTML = '<i class="fas fa-plus-square"></i>';
            btn.title = "Expand";
          }
        }
      });
    }
  });
}

function createTruncatedCell(text, maxLen = 50) {
  if (text === null || typeof text === "undefined") text = "";
  const stringText = String(text);
  const escaped = escapeHtml(stringText);

  const uid = "cell_" + Math.random().toString(36).slice(2, 10);

  if (escaped.length <= maxLen) {
    return `<span class="simple-cell">${escaped}</span>`;
  }

  const displayText = escaped.substring(0, maxLen) + "...";

  // Debug log (can be removed later)
  console.log(
    `Creating expandable cell for text: "${escaped.substring(
      0,
      20
    )}..." (length: ${escaped.length})`
  );

  return `
    <div class="expandable-cell" id="${uid}_container">
      <div class="cell-content">
        <span id="${uid}_truncated" class="cell-truncated-text">${displayText}</span>
        <span id="${uid}_full" class="cell-full-text" style="display: none;">${escaped}</span>
      </div>
      <button class="expand-toggle-btn" onclick="toggleCellText('${uid}', event)" title="Expand">
        <i class="fas fa-plus-square"></i>
      </button>
    </div>
  `;
}

// Add a function to clear all filters
function clearAllFilters() {
  // Clear search input
  const searchInput = document.getElementById("userSearch");
  if (searchInput) {
    searchInput.value = "";
  }

  // Clear date range
  const dateRange = document.getElementById("dateRange");
  if (dateRange && dateRange._flatpickr) {
    dateRange._flatpickr.clear();
  }

  // Reset to first page and render table
  currentPage = 1;
  renderTable();
}

window.toggleCellText = function (id, event) {
  event.stopPropagation();

  const container = document.getElementById(id + "_container");
  const truncatedSpan = document.getElementById(id + "_truncated");
  const fullSpan = document.getElementById(id + "_full");
  const btn = event.currentTarget;

  if (!container || !truncatedSpan || !fullSpan || !btn) {
    console.error("Could not find required elements for cell expansion");
    return;
  }

  const isExpanded = container.classList.contains("expanded");

  if (isExpanded) {
    // Collapse the cell
    container.classList.remove("expanded");
    truncatedSpan.style.display = "block";
    fullSpan.style.display = "none";
    btn.innerHTML = '<i class="fas fa-plus-square"></i>';
    btn.title = "Expand";

    // Reset any table cell height constraints
    const tableCell = container.closest("td");
    if (tableCell) {
      tableCell.style.height = "";
      tableCell.style.verticalAlign = "top";
    }
  } else {
    // First, collapse any other expanded cells in the same table
    const table = container.closest("table");
    if (table) {
      table
        .querySelectorAll(".expandable-cell.expanded")
        .forEach((otherContainer) => {
          const otherId = otherContainer.id.replace("_container", "");
          const otherTruncated = document.getElementById(
            otherId + "_truncated"
          );
          const otherFull = document.getElementById(otherId + "_full");
          const otherBtn = otherContainer.querySelector(".expand-toggle-btn");

          if (otherContainer !== container) {
            otherContainer.classList.remove("expanded");
            if (otherTruncated) otherTruncated.style.display = "block";
            if (otherFull) otherFull.style.display = "none";
            if (otherBtn) {
              otherBtn.innerHTML = '<i class="fas fa-plus-square"></i>';
              otherBtn.title = "Expand";
            }

            // Reset other cell heights
            const otherCell = otherContainer.closest("td");
            if (otherCell) {
              otherCell.style.height = "";
              otherCell.style.verticalAlign = "top";
            }
          }
        });
    }

    // Expand this cell
    container.classList.add("expanded");
    truncatedSpan.style.display = "none";
    fullSpan.style.display = "block";
    btn.innerHTML = '<i class="fas fa-minus-square"></i>';
    btn.title = "Collapse";

    // Adjust table cell to accommodate expanded content
    const tableCell = container.closest("td");
    if (tableCell) {
      tableCell.style.verticalAlign = "top";
      // Allow the cell to grow naturally with content
      tableCell.style.height = "auto";
    }
  }
};

// Note: Removed positionTooltip function as we now use inline expansion

// Helper function to check if a column is a scoring column
function isScoreColumn(columnName, columnDef, cellValue = null) {
  // Check if it's a question column with scoring pattern (qX_Y (TX))
  if (
    !columnDef ||
    columnDef.type !== "question" ||
    !/^q\d+_\d+\s+\(T[123]\)$/.test(columnName)
  ) {
    return false;
  }

  // Additional check: if we have cell value, verify it's actually numeric/short
  if (cellValue !== null) {
    const stringValue = String(cellValue).trim();
    // Only consider it a score column if the value is:
    // - Empty, or
    // - A single digit (0-9), or
    // - A short numeric value (1-2 digits), or
    // - Common score indicators like "N/A", "-", etc.
    return (
      stringValue === "" ||
      /^[0-9]{1,2}$/.test(stringValue) ||
      /^(N\/A|n\/a|-|—)$/.test(stringValue)
    );
  }

  // If no cell value provided, use the pattern match (for headers)
  return true;
}

const openEndedResponsesConfig = {
  t1_strategy: { baseName: "Strategy (T1)", groups: ["A", "B"] }, // Groups A/B have T1 reflection
  t2_course_list: { baseName: "Courses (T2)", groups: ["A", "B"] }, // Groups A/B have course list input in T2
  t2_course_feedback: { baseName: "Course Feedback (T2)", groups: ["A", "B"] }, // Groups A/B have course feedback input in T2
  t2_reflection: { baseName: "Reflection (T2)", groups: ["A", "B", "C", "D"] }, // All groups seem to have a T2 reflection (key might be reused)
  t3_reflection: { baseName: "Reflection (T3)", groups: ["A", "B", "C", "D"] }, // All groups seem to have a T3 reflection (key might be reused)
  t3_additional_reflection: {
    baseName: "Additional Reflection (T3)",
    groups: ["B", "D"],
  }, // Only Groups B/D have the second T3 reflection step
  t3_email: { baseName: "Email (T3)", groups: ["A", "B"] }, // Groups A/B have email input in T3
};

function getQuestionTextById(qId) {
  if (typeof surveyData !== "undefined" && Array.isArray(surveyData)) {
    for (const section of surveyData) {
      if (section.questions && Array.isArray(section.questions)) {
        const question = section.questions.find((q) => q.id === qId);
        if (question && question.text) {
          const tempDiv = document.createElement("div");
          tempDiv.innerHTML = question.text;
          let textContent = tempDiv.textContent || tempDiv.innerText || "";
          return textContent.trim().replace(/\s+/g, " ") || qId;
        }
      }
    }
  }
  if (
    qId.startsWith("q-2_") &&
    typeof preSurveyQuestions !== "undefined" &&
    preSurveyQuestions.questions
  ) {
    const index = parseInt(qId.split("_")[1], 10);
    if (
      preSurveyQuestions.questions[index] &&
      preSurveyQuestions.questions[index].text
    ) {
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = preSurveyQuestions.questions[index].text;
      let textContent = tempDiv.textContent || tempDiv.innerText || "";
      return textContent.trim().replace(/\s+/g, " ") || qId;
    }
  }
  return qId;
}

function initializeFlatpickr() {
  const dateRangePicker = document.getElementById("dateRange");
  if (dateRangePicker) {
    flatpickr(dateRangePicker, {
      mode: "range",
      dateFormat: "Y-m-d",
      altInput: true,
      altFormat: "F j, Y",
      onChange: function (selectedDates, dateStr, instance) {
        // When dates change, re-filter and render the table
        if (selectedDates.length === 2 || selectedDates.length === 0) {
          // Trigger on selection or clear
          currentPage = 1;
          renderTable();
        }
      },
    });
  }
}

document.addEventListener("DOMContentLoaded", async () => {
  addTruncationStyles();
  try {
    await fetchData(); // Populates global `users` and `questionIds`
    // surveyData is already global from the script include

    initializeCodebookMap(); // IMPORTANT: Call this AFTER surveyData and questionIds are ready

    initializeFlatpickr();
    initSearchListener();
    initClearFiltersButton();
    renderTable();
    initExportButton();
    initChart();
  } catch (err) {
    console.error("Error initializing dashboard:", err);
    showError(
      "Failed to initialize dashboard: " + (err.message || "Unknown error")
    );
  }
});

// Function to initialize the clear filters button
function initClearFiltersButton() {
  const dateRangeInput = document.getElementById("dateRange");
  if (dateRangeInput) {
    const dateRangeContainer = dateRangeInput.closest(".input-group"); // Find the parent input-group
    if (dateRangeContainer && !document.getElementById("clearFiltersBtn")) {
      // Check if button already exists
      const clearBtn = document.createElement("button");
      clearBtn.id = "clearFiltersBtn";
      clearBtn.className = "btn btn-outline-danger btn-sm"; // Use outline-danger for visibility and btn-sm for consistency
      clearBtn.innerHTML = '<i class="fas fa-times me-1"></i> Clear'; // Simpler text
      clearBtn.title = "Clear search and date filters";

      // Append button inside the input-group for better alignment
      dateRangeContainer.appendChild(clearBtn);

      // Add event listener to clear button
      clearBtn.addEventListener("click", clearAllFilters);
    }
  }
}

// Function to initialize search listeners
function initSearchListener() {
  const searchInput = document.getElementById("userSearch");
  if (searchInput) {
    let debounceTimer;
    searchInput.addEventListener("input", function () {
      clearTimeout(debounceTimer);
      debounceTimer = setTimeout(() => {
        console.log("Search triggered for:", this.value);
        currentPage = 1;
        renderTable();
      }, 150); // Reduced debounce time for more real-time feel (was 300ms)
    });

    // Add placeholder text to indicate real-time search
    searchInput.placeholder = "Search users...";
  }

  // Search button listener (optional, if you want explicit search trigger)
  const searchButton = document.getElementById("searchButton");
  if (searchButton) {
    searchButton.addEventListener("click", () => {
      const searchInput = document.getElementById("userSearch");
      console.log(
        "Search button clicked, searching for:",
        searchInput?.value || ""
      );
      currentPage = 1;
      renderTable();
    });
  }
}

// Function to initialize export button
function initExportButton() {
  document
    .getElementById("exportSelected")
    ?.addEventListener("click", () => exportSelectedData(false)); // Pass false for selected
  document.getElementById("exportAll")?.addEventListener("click", () => {
    // Confirmation for exporting all users, as it might be a large file
    Swal.fire({
      title: "Export All Users?",
      text: `This will export data for all ${users.length} users. Continue?`,
      icon: "question",
      showCancelButton: true,
      confirmButtonText: "Yes, export all",
      cancelButtonText: "No, cancel",
    }).then((result) => {
      if (result.isConfirmed) {
        exportSelectedData(true); // Pass true for all
      }
    });
  });
}

// Function to initialize chart and visualization
function initChart() {
  console.log("Initializing chart and visualization");

  // Visualization buttons
  const toggleBtn = document.getElementById("toggleVisualization");
  const closeBtn = document.getElementById("closeVisualization");
  const overlay = document.getElementById("visualizationOverlay");

  if (toggleBtn) {
    console.log("Adding click listener to toggleVisualization button");
    // Remove any existing listeners to prevent duplicates
    toggleBtn.removeEventListener("click", toggleVisualizationHandler);
    toggleBtn.addEventListener("click", toggleVisualizationHandler);
  } else {
    console.error("toggleVisualization button not found");
  }

  if (closeBtn) {
    closeBtn.removeEventListener("click", closeVisualization);
    closeBtn.addEventListener("click", closeVisualization);
  }

  if (overlay) {
    overlay.removeEventListener("click", closeVisualization);
    overlay.addEventListener("click", closeVisualization);
  }
}

// Handler for toggle visualization button
function toggleVisualizationHandler() {
  console.log("Toggle visualization clicked");
  const vs = document.getElementById("visualizationSidebar");
  if (vs?.classList.contains("active")) {
    closeVisualization();
  } else {
    openVisualization();
  }
}

// Initialize date range picker (placeholder - implement as needed)
function initDateRangePicker() {
  // Implementation depends on your date picker library
  // This is just a placeholder
}

document.addEventListener("DOMContentLoaded", async () => {
  addTruncationStyles();

  try {
    // Fetch initial data
    //surveyData = await fetchData();
    if (surveyData && surveyData.questions) {
      questionIds = Object.keys(surveyData.questions);
    }

    initDateRangePicker();
    initSearchListener();
    initClearFiltersButton();

    renderTable(); // First render
    initExportButton(); // CSV export
    initChart(); // If you have charts
  } catch (err) {
    console.error("Error initializing dashboard:", err);
    showError("Failed to initialize dashboard");
  }
});

function getAuthToken() {
  return localStorage.getItem("dashboardToken") || "";
}

async function fetchData() {
  try {
    document.getElementById("loadingIndicator").style.display = "block";
    const res = await fetch("php/dashboard-data.php", {
      headers: { Authorization: `Bearer ${getAuthToken()}` },
    });
    if (!res.ok) {
      const errorText = await res.text();
      console.error("Server response:", errorText);
      throw new Error(`Server returned status ${res.status}`);
    }
    const data = await res.json();
    if (data.error) {
      throw new Error(data.message || "Unknown server error");
    }
    if (!Array.isArray(data.users)) {
      console.error("Invalid data structure:", data);
      throw new Error(
        "Invalid data structure from server - users array missing"
      );
    }
    questionIds = data.questionIds || [];
    if (!Array.isArray(questionIds)) {
      console.warn("Server did not provide questionIds, using empty array");
      questionIds = [];
    }
    users = data.users.map((u) => ({
      ...u,
      userCode: u.userCode || u.user_code || "Unknown",
      attemptNumber: u.attemptNumber || u.attempt_number || 1,
      preSurveyResponses: u.preSurveyResponses || {},
      openEndedResponses: u.openEndedResponses || {},
      timeStamps: u.timeStamps || { t1: {}, t2: {}, t3: {} }, // Ensure structure
      initialResponses: u.initialResponses || {},
      updatedResponses: u.updatedResponses || {},
      followUpResponses: u.followUpResponses || {},
      initialScores: u.initialScores || {},
      updatedScores: u.updatedScores || {},
      followUpScores: u.followUpScores || {},
    }));
    console.log("Successfully loaded users:", users.length);
    document.getElementById("loadingIndicator").style.display = "none";
    return data;
  } catch (err) {
    console.error("Error fetching data:", err);
    showError("Failed to load user data: " + err.message);
    document.getElementById("loadingIndicator").style.display = "none";
    throw err;
  }
}

function updateSelectAllCheckboxState() {
  const selectAllCheckbox = document.getElementById("selectAllCheckbox");
  if (!selectAllCheckbox) return;

  // Only consider checkboxes of currently *rendered* rows in the tbody
  const userCheckboxes = document.querySelectorAll(
    "#userTable tbody tr .user-select"
  );
  if (userCheckboxes.length === 0) {
    selectAllCheckbox.checked = false;
    selectAllCheckbox.indeterminate = false;
    return;
  }

  const totalVisible = userCheckboxes.length;
  const totalChecked = Array.from(userCheckboxes).filter(
    (cb) => cb.checked
  ).length;

  if (totalChecked === 0) {
    selectAllCheckbox.checked = false;
    selectAllCheckbox.indeterminate = false;
  } else if (totalChecked === totalVisible) {
    selectAllCheckbox.checked = true;
    selectAllCheckbox.indeterminate = false;
  } else {
    // Some are checked, but not all
    selectAllCheckbox.checked = false;
    selectAllCheckbox.indeterminate = true;
  }
}

function renderTable() {
  const theadRow = document.querySelector("#userTable thead tr");
  const tbody = document.querySelector("#userTable tbody");
  if (!theadRow || !tbody) {
    console.error("Table head or body not found!");
    return;
  }

  const filtered = filterUsers();
  const startIndex = (currentPage - 1) * usersPerPage;
  const endIndex = startIndex + usersPerPage;
  const displaySet = filtered.slice(startIndex, endIndex);

  theadRow.innerHTML = "";
  tbody.innerHTML = "";

  const columnOrder = Object.keys(codebookColumnMap);

  // Render Headers
  theadRow.innerHTML =
    '<th><input type="checkbox" id="selectAllCheckbox" class="form-check-input"></th>' +
    columnOrder
      .map((columnName) => {
        const columnDef = codebookColumnMap[columnName];
        const titleAttr =
          columnDef && columnDef.prompt
            ? `title="${escapeHtml(columnDef.prompt)}"`
            : "";
        const scoreClass = isScoreColumn(columnName, columnDef)
          ? ' class="score-column"'
          : "";
        return `<th${scoreClass} ${titleAttr}>${escapeHtml(columnName)}</th>`;
      })
      .join("");

  // Render Body
  if (!displaySet.length) {
    const r = tbody.insertRow();
    const c = r.insertCell();
    c.colSpan = columnOrder.length + 1;
    c.innerText = "No matching user data found.";
    c.style.textAlign = "center";
  } else {
    displaySet.forEach((user) => {
      const tr = tbody.insertRow();
      // Checkbox cell
      const selectCell = tr.insertCell();
      selectCell.innerHTML = `<input type="checkbox" class="user-select form-check-input" data-user-code="${escapeHtml(
        user.userCode
      )}">`;
      selectCell.style.textAlign = "center";

      columnOrder.forEach((columnName) => {
        const td = tr.insertCell();
        let cellValue = "";
        const columnDef = codebookColumnMap[columnName];

        // We'll determine if this is a score cell after getting the value

        if (columnDef) {
          switch (columnDef.type) {
            case "data":
              cellValue = safeAccess(user, columnDef.source);
              if (columnDef.format === "date" && cellValue) {
                cellValue = formatDate(cellValue);
              }
              break;
            case "calculated":
              if (columnName === "Gruppe") {
                const participation = safeAccess(
                  user,
                  "preSurveyResponses.q-2_0"
                );
                cellValue =
                  participation === "Nein"
                    ? "Gruppe A"
                    : safeAccess(user, "preSurveyResponses.q-2_1") ||
                      "Gruppe A";
              } else if (columnName === "Datenschutz accepted?") {
                cellValue =
                  user.datenschutzConsent || user.timeStamps?.t1?.start
                    ? "Yes"
                    : "No";
              }
              break;
            case "openEnded":
              const userGroupLetter =
                safeAccess(user, "preSurveyResponses.q-2_0") === "Nein"
                  ? "A"
                  : (
                      safeAccess(user, "preSurveyResponses.q-2_1") || "Gruppe A"
                    ).replace("Gruppe ", "");
              if (columnDef.group === userGroupLetter) {
                cellValue = safeAccess(
                  user,
                  `openEndedResponses.${columnDef.oerKey}`
                );
              } else {
                cellValue = "";
              }
              break;
            case "question":
              let response = safeAccess(
                user,
                `${columnDef.attemptKey}.${columnDef.qId}`
              );
              if (Array.isArray(response)) response = response.join(", ");
              else if (typeof response === "string" && response.includes("|"))
                response = response.split("|").join(", ");
              cellValue = response || "";
              break;
            case "score":
              cellValue = safeAccess(user, columnDef.source);
              break;
            case "calculatedDuration":
              const attempt = columnDef.attempt;
              const start = user.timeStamps[`t${attempt}`]?.start;
              const end = user.timeStamps[`t${attempt}`]?.end;
              if (start && end) {
                try {
                  const startDate = new Date(start);
                  const endDate = new Date(end);
                  if (
                    !isNaN(startDate) &&
                    !isNaN(endDate) &&
                    endDate >= startDate
                  ) {
                    const diffMs = endDate - startDate;
                    const diffMins = Math.round(diffMs / 60000);
                    cellValue = `${diffMins} min`;
                  } else {
                    cellValue = "N/A";
                  }
                } catch (e) {
                  console.warn("Error calculating duration:", start, end, e);
                  cellValue = "Error";
                }
              } else {
                cellValue = "N/A";
              }
              break;
            default:
              cellValue = "";
          }
        }

        // Now check if this is actually a score column based on the content
        const isActualScoreColumn = isScoreColumn(
          columnName,
          columnDef,
          cellValue
        );

        if (isActualScoreColumn) {
          // Apply score-cell class and don't use truncation since they're single digits
          td.className = "score-cell";
          td.innerHTML = `<span class="simple-cell">${escapeHtml(
            cellValue || ""
          )}</span>`;
        } else {
          // Use standard maxLen for text truncation
          td.innerHTML = createTruncatedCell(cellValue || "", 50);
        }
      });
    });
  }
  updatePagination(filtered.length);
  setupSelectAllFeature();
  updateSelectAllCheckboxState();
}

// Helper function to get all column definitions for searching
function getDashboardColumnDefinitions() {
  // Convert codebookColumnMap to an array of column definitions
  const columnDefinitions = [];

  for (const [columnName, columnDef] of Object.entries(codebookColumnMap)) {
    columnDefinitions.push({
      key: columnName,
      ...columnDef,
    });
  }

  return columnDefinitions;
}

// Updated filterUsers function with improved search
function filterUsers() {
  console.log("Filtering users...");
  const searchTerm = (document.getElementById("userSearch")?.value || "")
    .toLowerCase()
    .trim();
  const dateRangePicker = document.getElementById("dateRange")?._flatpickr; // Use the flatpickr instance
  let startDate = null;
  let endDate = null;

  if (dateRangePicker && dateRangePicker.selectedDates.length === 2) {
    startDate = new Date(dateRangePicker.selectedDates[0]);
    startDate.setHours(0, 0, 0, 0); // Start of the day
    endDate = new Date(dateRangePicker.selectedDates[1]);
    endDate.setHours(23, 59, 59, 999); // End of the day
  }

  return users.filter((user) => {
    // Date range filtering
    let dateMatch = true;
    if (startDate && endDate) {
      let userHasDateInRange = false;
      const timestampsToCheck = [
        user.timeStamps?.t1,
        user.timeStamps?.t2,
        user.timeStamps?.t3,
      ];
      for (const ts of timestampsToCheck) {
        if (ts) {
          try {
            const userDate = new Date(ts);
            if (
              !isNaN(userDate.getTime()) &&
              userDate >= startDate &&
              userDate <= endDate
            ) {
              userHasDateInRange = true;
              break;
            }
          } catch (e) {
            console.warn("Error parsing date for filtering:", ts, e);
          }
        }
      }
      dateMatch = userHasDateInRange;
    }
    if (!dateMatch) return false;

    // Text search filtering
    if (searchTerm) {
      // Create a comprehensive searchable string from all user data
      let searchableContent = [];

      // Add basic user properties
      if (user.userCode) searchableContent.push(user.userCode.toLowerCase());
      if (user.attemptNumber)
        searchableContent.push(user.attemptNumber.toString());

      // Add pre-survey responses
      if (user.preSurveyResponses) {
        Object.values(user.preSurveyResponses).forEach((value) => {
          if (value) {
            if (typeof value === "string")
              searchableContent.push(value.toLowerCase());
            else if (typeof value === "number")
              searchableContent.push(value.toString());
          }
        });
      }

      // Add open-ended responses
      if (user.openEndedResponses) {
        Object.values(user.openEndedResponses).forEach((value) => {
          if (value && typeof value === "string")
            searchableContent.push(value.toLowerCase());
        });
      }

      // Add all survey responses (T1, T2, T3)
      ["initialResponses", "updatedResponses", "followUpResponses"].forEach(
        (responseKey) => {
          if (user[responseKey]) {
            Object.values(user[responseKey]).forEach((value) => {
              if (value) {
                if (typeof value === "string") {
                  // Handle pipe-separated values
                  if (value.includes("|")) {
                    value.split("|").forEach((part) => {
                      searchableContent.push(part.toLowerCase().trim());
                    });
                  } else {
                    searchableContent.push(value.toLowerCase());
                  }
                } else if (Array.isArray(value)) {
                  value.forEach((item) => {
                    if (item && typeof item === "string")
                      searchableContent.push(item.toLowerCase());
                  });
                } else if (typeof value === "number") {
                  searchableContent.push(value.toString());
                }
              }
            });
          }
        }
      );

      // Check if any of the searchable content includes the search term
      if (!searchableContent.some((content) => content.includes(searchTerm))) {
        return false;
      }
    }
    return true;
  });
}

function updatePagination(totalCount) {
  const paginationEl = document.getElementById("pagination");
  if (!paginationEl) return;

  const totalPages = Math.ceil(totalCount / usersPerPage);
  currentPage = Math.min(currentPage, totalPages) || 1; // Ensure currentPage is valid

  paginationEl.innerHTML = `
    <button id="prevPage" class="btn btn-outline-secondary" ${
      currentPage === 1 ? "disabled" : ""
    }>Prev</button>
    <span class="mx-2 pagination-info">Page ${currentPage} of ${
    totalPages > 0 ? totalPages : 1
  }</span>
    <button id="nextPage" class="btn btn-outline-secondary" ${
      currentPage === totalPages || totalPages === 0 ? "disabled" : ""
    }>Next</button>
  `;
  // Remove old listeners before adding new ones to prevent duplicates
  const prevButton = document.getElementById("prevPage");
  const nextButton = document.getElementById("nextPage");

  if (prevButton) {
    const newPrevButton = prevButton.cloneNode(true);
    prevButton.parentNode.replaceChild(newPrevButton, prevButton);
    if (currentPage > 1) {
      newPrevButton.addEventListener("click", () => changePage(-1));
    } else {
      newPrevButton.disabled = true;
    }
  }
  if (nextButton) {
    const newNextButton = nextButton.cloneNode(true);
    nextButton.parentNode.replaceChild(newNextButton, nextButton);
    if (currentPage < totalPages) {
      newNextButton.addEventListener("click", () => changePage(1));
    } else {
      newNextButton.disabled = true;
    }
  }
}

function changePage(direction) {
  // No need to call filterUsers again, renderTable will do it
  const filteredCount = filterUsers().length; // Need count for totalPages calculation
  const totalPages = Math.ceil(filteredCount / usersPerPage);
  const newPage = currentPage + direction;

  // Only update if the new page is within valid bounds
  if (newPage >= 1 && newPage <= totalPages) {
    currentPage = newPage;
    renderTable();
  }
}

function showError(msg) {
  const el = document.getElementById("errorMessage");
  if (el) {
    el.textContent = msg;
    el.style.display = "block";
    // Optionally auto-hide after some time
    // setTimeout(() => { el.style.display = 'none'; }, 5000);
  } else {
    console.error(msg); // Fallback to console
  }
}

function openVisualization() {
  const sidebar = document.getElementById("visualizationSidebar");
  const overlay = document.getElementById("visualizationOverlay");
  const toggleBtn = document.getElementById("toggleVisualization");
  if (!sidebar || !overlay || !toggleBtn) return;

  console.log("Opening visualization sidebar");
  sidebar.classList.add("active");
  overlay.classList.add("active");
  toggleBtn.innerHTML =
    '<i class="fas fa-chart-line me-1"></i>Hide Visualization';
  updateVisualization(); // Update chart when opening

  // Add listener for download button only when sidebar is open
  const downloadButton = document.getElementById("downloadVisualization");
  if (downloadButton) {
    // Clone to remove previous listeners if any
    const newDownloadButton = downloadButton.cloneNode(true);
    downloadButton.parentNode.replaceChild(newDownloadButton, downloadButton);
    newDownloadButton.addEventListener("click", downloadVisualizationChart);
  }
}

function closeVisualization() {
  const sidebar = document.getElementById("visualizationSidebar");
  const overlay = document.getElementById("visualizationOverlay");
  const toggleBtn = document.getElementById("toggleVisualization");
  if (!sidebar || !overlay || !toggleBtn) return;

  console.log("Closing visualization sidebar");
  sidebar.classList.remove("active");
  overlay.classList.remove("active");
  toggleBtn.innerHTML =
    '<i class="fas fa-chart-line me-1"></i>Show Visualization';
}

// Find the updateVisualization function and replace it with this updated version
function updateVisualization() {
  const canvas = document.getElementById("visualization");
  if (!canvas) return;

  const checked = [...document.querySelectorAll(".user-select:checked")];
  if (!checked.length) {
    if (window.myChart) {
      window.myChart.destroy();
      window.myChart = null; // Clear reference
    }
    return;
  }

  const lastChecked = checked[checked.length - 1]; // Get the *last* checked box
  const userCode = lastChecked.dataset.userCode;
  const user = users.find((u) => u.userCode === userCode);

  if (!user) {
    if (window.myChart) {
      window.myChart.destroy();
      window.myChart = null;
    }
    return;
  }

  // Use initialScores, updatedScores, and followUpScores directly
  const t1 = user.initialScores || {};
  const t2 = user.updatedScores || {};
  const t3 = user.followUpScores || {};

  const allCats = new Set([
    ...Object.keys(t1),
    ...Object.keys(t2),
    ...Object.keys(t3),
  ]);
  allCats.delete("overall");

  // Try to use sortCategories from script.min.js if available, otherwise use a fallback
  let categories;
  if (typeof window.sortCategories === "function") {
    categories = window.sortCategories(Array.from(allCats));
  } else {
    // Fallback sorting based on the categoryOrder defined in script.min.js
    const categoryOrder = [
      "Suchen, Verarbeiten und Aufbewahren", // suchen
      "Kommunikation und Kollaborieren", // komm
      "Produzieren und Präsentieren", // prod
      "Schützen und sicher Agieren", // schutz
      "Problemlösen und Handeln", // problem
      "Analysieren und Reflektieren", // anal
    ];

    // Sort based on the order in categoryOrder
    categories = Array.from(allCats).sort((a, b) => {
      const indexA = categoryOrder.indexOf(a);
      const indexB = categoryOrder.indexOf(b);
      if (indexA === -1 && indexB === -1) return a.localeCompare(b);
      if (indexA === -1) return 1;
      if (indexB === -1) return -1;
      return indexA - indexB;
    });
  }

  const t1Data = categories.map((cat) => t1[cat] || 0);
  const t2Data = categories.map((cat) => t2[cat] || 0);
  const t3Data = categories.map((cat) => t3[cat] || 0);

  // Use the shared chart configuration function from script.min.js
  const chartConfig = createCompetencyChartConfig(
    categories,
    t1Data,
    t2Data,
    t3Data,
    "visualization",
    null, // No description box
    userCode // User code for display
  );

  if (window.myChart) {
    window.myChart.data.labels = chartConfig.data.labels;
    window.myChart.data.datasets = chartConfig.data.datasets;
    window.myChart.options = chartConfig.options;
    window.myChart.update();
  } else {
    const ctx = canvas.getContext("2d");
    window.myChart = new Chart(ctx, chartConfig);
  }

  // Update user code display
  const userCodeSpan = document.getElementById("visualizationUserCode");
  if (userCodeSpan) {
    userCodeSpan.textContent = userCode;
  }
}

function downloadVisualizationChart() {
  const canvas = document.getElementById("visualization");
  if (!canvas || !window.myChart) {
    // Check if chart exists
    console.error("Canvas element or chart not found.");
    Swal.fire(
      "Error",
      "Could not download chart. Visualization not available.",
      "error"
    );
    return;
  }

  const userCodeSpan = document.getElementById("visualizationUserCode");
  const userCode = userCodeSpan ? userCodeSpan.textContent : "chart";

  try {
    const link = document.createElement("a");
    link.download = `chart_${userCode}.png`;
    link.href = canvas.toDataURL("image/png", 1.0); // Use high quality PNG
    link.click();
  } catch (e) {
    console.error("Error generating chart image:", e);
    Swal.fire("Error", "Failed to generate chart image for download.", "error");
  }
}

function escapeCsvValue(value) {
  if (value == null) return ""; // Handle null or undefined
  const stringValue = String(value);
  let escapedValue = stringValue.replace(/"/g, '""'); // Escape double quotes with double quotes
  if (
    escapedValue.includes(",") ||
    escapedValue.includes("\n") ||
    escapedValue.includes('"')
  ) {
    escapedValue = `"${escapedValue}"`; // Enclose in double quotes
  }
  return escapedValue;
}

async function exportSelectedData(exportAll = false) {
  let usersToExport = [];
  if (exportAll) {
    usersToExport = [...users];
    if (usersToExport.length === 0) {
      Swal.fire("No Data", "No user data available to export.", "info");
      return;
    }
  } else {
    document
      .querySelectorAll("#userTable tbody .user-select:checked")
      .forEach((checkbox) => {
        const userCode = checkbox.dataset.userCode;
        const user = users.find((u) => u.userCode === userCode);
        if (user) usersToExport.push(user);
      });
    if (usersToExport.length === 0) {
      Swal.fire("No Users Selected", "Please select users to export.", "info");
      return;
    }
  }

  Swal.fire({
    title: "Generating Excel File",
    text: "Please wait, this may take a moment...",
    allowOutsideClick: false,
    didOpen: () => {
      Swal.showLoading();
    },
  });

  try {
    const workbook = await XlsxPopulate.fromBlankAsync();
    const sheet = workbook.sheet(0);

    const columnOrder = Object.keys(codebookColumnMap);
    sheet.cell("A1").value([columnOrder]);

    const rows = usersToExport.map((user) => {
      const rowData = [];
      columnOrder.forEach((columnName) => {
        let cellValue = "";
        const columnDef = codebookColumnMap[columnName];
        if (columnDef) {
          switch (columnDef.type) {
            case "data":
              cellValue = safeAccess(user, columnDef.source);
              if (columnDef.format === "date" && cellValue)
                cellValue = formatDate(cellValue);
              break;
            case "calculated":
              if (columnName === "Gruppe") {
                const participation = safeAccess(
                  user,
                  "preSurveyResponses.q-2_0"
                );
                cellValue =
                  participation === "Nein"
                    ? "Gruppe A"
                    : safeAccess(user, "preSurveyResponses.q-2_1") ||
                      "Gruppe A";
              } else if (columnName === "Datenschutz accepted?") {
                cellValue =
                  user.datenschutzConsent || user.timeStamps?.t1?.start
                    ? "Yes"
                    : "No";
              }
              break;
            case "openEnded":
              const userGroupLetter = (
                safeAccess(user, "preSurveyResponses.q-2_1") || "Gruppe A"
              ).replace("Gruppe ", "");
              if (columnDef.group === userGroupLetter)
                cellValue = safeAccess(
                  user,
                  `openEndedResponses.${columnDef.oerKey}`
                );
              else cellValue = "";
              break;
            case "question":
              let response = safeAccess(
                user,
                `${columnDef.attemptKey}.${columnDef.qId}`
              );
              if (Array.isArray(response)) response = response.join("; ");
              else if (typeof response === "string" && response.includes("|"))
                response = response.split("|").join("; ");
              cellValue = response || "";
              break;
            case "calculatedDuration":
              const attempt = columnDef.attempt;
              const start = user.timeStamps[`t${attempt}`]?.start;
              const end = user.timeStamps[`t${attempt}`]?.end;
              if (start && end) {
                try {
                  const diffMins = Math.round(
                    (new Date(end) - new Date(start)) / 60000
                  );
                  cellValue = diffMins;
                } catch (e) {
                  cellValue = "Error";
                }
              } else {
                cellValue = "";
              }
              break;
            default:
              cellValue = "";
          }
        }
        rowData.push(cellValue);
      });
      return rowData;
    });

    if (rows.length > 0) {
      sheet.cell("A2").value(rows);
    }

    // --- CORRECTED FORMATTING LOGIC ---
    // Helper function to convert column number to Excel column letter
    function numberToColumnName(num) {
      let result = "";
      while (num > 0) {
        num--;
        result = String.fromCharCode(65 + (num % 26)) + result;
        num = Math.floor(num / 26);
      }
      return result;
    }

    const headerRange = sheet.range(
      "A1",
      `${numberToColumnName(columnOrder.length)}1`
    );
    headerRange.style({
      bold: true,
      fill: "D9D9D9",
    });

    sheet.freezePanes(0, 1);

    // CORRECTED a few lines below - Set different widths based on actual content analysis
    columnOrder.forEach((columnName, index) => {
      const columnDef = codebookColumnMap[columnName];

      // Analyze the actual data in this column to determine if it's truly a score column
      let isActuallyScoreColumn = false;

      if (
        columnDef &&
        columnDef.type === "question" &&
        /^q\d+_\d+\s+\(T[123]\)$/.test(columnName)
      ) {
        // Sample a few values from this column to determine content type
        const sampleSize = Math.min(10, usersToExport.length);
        let numericCount = 0;

        for (let i = 0; i < sampleSize; i++) {
          const user = usersToExport[i];
          let cellValue = "";

          // Get the cell value using the same logic as table rendering
          switch (columnDef.type) {
            case "question":
              let response = safeAccess(
                user,
                `${columnDef.attemptKey}.${columnDef.qId}`
              );
              if (Array.isArray(response)) response = response.join(", ");
              else if (typeof response === "string" && response.includes("|"))
                response = response.split("|").join(", ");
              cellValue = response || "";
              break;
            case "score":
              cellValue = safeAccess(user, columnDef.source);
              break;
            default:
              cellValue = "";
          }

          // Check if this value looks like a score
          if (isScoreColumn(columnName, columnDef, cellValue)) {
            numericCount++;
          }
        }

        // If most sampled values are numeric/short, treat as score column
        isActuallyScoreColumn = numericCount / sampleSize >= 0.7;
      }

      // Use sheet.column() not workbook.column()
      if (isActuallyScoreColumn) {
        sheet.column(index + 1).width(12); // Wider to fit column headers in single line
      } else {
        sheet.column(index + 1).width(30); // Default width for other columns
      }
    });

    const usedRange = sheet.usedRange();
    if (usedRange) {
      usedRange.style({
        wrapText: true,
        verticalAlignment: "top",
        horizontalAlignment: "left",
      });
    }
    // --- END OF CORRECTION ---

    const blob = await workbook.outputAsync();
    const fileName = `OpenDigi_SurveyExport_${new Date()
      .toISOString()
      .slice(0, 10)}.xlsx`;

    if (window.navigator && window.navigator.msSaveOrOpenBlob) {
      window.navigator.msSaveOrOpenBlob(blob, fileName);
    } else {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      document.body.appendChild(a);
      a.href = url;
      a.download = fileName;
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    }

    Swal.close();
  } catch (err) {
    console.error("Error generating Excel file:", err);
    Swal.fire({
      icon: "error",
      title: "Export Failed",
      text: "Could not generate the Excel file. Please try again.",
    });
  }
}

function setupSelectAllFeature() {
  const tableHeader = document.querySelector("#userTable thead tr");
  if (!tableHeader || tableHeader.cells.length === 0) return;

  const firstCell = tableHeader.cells[0];
  if (!firstCell) return;

  let selectAllCheckbox = document.getElementById("selectAllCheckbox");
  if (!selectAllCheckbox) {
    firstCell.innerHTML = `
      <div class="select-all-container d-flex align-items-center">
        <input type="checkbox" id="selectAllCheckbox" class="form-check-input me-1" title="Select/Deselect All Visible">
        <label for="selectAllCheckbox" class="form-check-label small fw-bold" style="cursor:pointer;">All</label>
      </div>`;
    selectAllCheckbox = document.getElementById("selectAllCheckbox");
  }

  // Remove old listener before adding a new one to prevent duplicates
  selectAllCheckbox.removeEventListener("change", handleSelectAllChange);
  selectAllCheckbox.addEventListener("change", handleSelectAllChange);

  // "Select All/None" button in the controls section
  const controlsSection = document.querySelector(
    "section.controls-panel .d-flex"
  );
  let toggleSelectAllButton = document.getElementById("toggleSelectAllButton");

  if (controlsSection && !toggleSelectAllButton) {
    toggleSelectAllButton = document.createElement("button");
    toggleSelectAllButton.id = "toggleSelectAllButton";
    toggleSelectAllButton.className = "btn btn-outline-secondary btn-sm"; // Smaller button
    toggleSelectAllButton.innerHTML =
      '<i class="fas fa-check-double me-1"></i>Toggle All Visible';
    toggleSelectAllButton.title =
      "Toggle selection of all currently visible items";

    const exportAllButton = document.getElementById("exportAll");
    if (exportAllButton && exportAllButton.parentNode === controlsSection) {
      controlsSection.insertBefore(
        toggleSelectAllButton,
        exportAllButton.nextSibling
      );
    } else {
      controlsSection.appendChild(toggleSelectAllButton);
    }
  }

  if (toggleSelectAllButton) {
    toggleSelectAllButton.removeEventListener("click", handleToggleAllClick); // Remove old before adding
    toggleSelectAllButton.addEventListener("click", handleToggleAllClick);
  }
}

function handleSelectAllChange() {
  // Triggered by the header checkbox
  const isChecked = this.checked;
  // This event comes from the main "select all" checkbox.
  // If it's being checked (not indeterminate), check/uncheck all visible.
  const userCheckboxes = document.querySelectorAll(
    "#userTable tbody tr .user-select"
  );
  userCheckboxes.forEach((checkbox) => {
    checkbox.checked = isChecked;
  });
  this.indeterminate = false; // After click, it's either all checked or all unchecked
  updateVisualization(); // Update chart based on new selection
}

function handleToggleAllClick() {
  // Triggered by the "Toggle All Visible" button
  const userCheckboxes = document.querySelectorAll(
    "#userTable tbody tr .user-select"
  );
  if (userCheckboxes.length === 0) return;

  // Determine if the action should be to check all or uncheck all
  // If any are unchecked, the action is to check all. Otherwise, uncheck all.
  const shouldCheckAll = Array.from(userCheckboxes).some((cb) => !cb.checked);

  userCheckboxes.forEach((checkbox) => {
    checkbox.checked = shouldCheckAll;
  });

  updateSelectAllCheckboxState(); // Reflect the new state in the header checkbox
  updateVisualization();
}

function setupSelectAllFeature() {
  const tableHeader = document.querySelector("#userTable thead tr");
  if (!tableHeader || tableHeader.cells.length === 0) return;

  const firstCell = tableHeader.cells[0]; // Assuming the first cell is for the select all checkbox
  if (!firstCell) return;

  let selectAllCheckbox = document.getElementById("selectAllCheckbox");
  if (!selectAllCheckbox) {
    // Use Bootstrap classes for styling the checkbox and label
    firstCell.innerHTML = `
      <div class="select-all-container form-check d-flex justify-content-center align-items-center p-0 m-0">
        <input type="checkbox" id="selectAllCheckbox" class="form-check-input" title="Select/Deselect All Visible Rows" style="margin:0; padding:0;">
        <!-- <label for="selectAllCheckbox" class="form-check-label visually-hidden">Select All</label> -->
        <!-- Label can be visually hidden if checkbox is clear enough -->
      </div>`;
    selectAllCheckbox = document.getElementById("selectAllCheckbox");
  }

  if (selectAllCheckbox) {
    selectAllCheckbox.removeEventListener("change", handleSelectAllChange); // Prevent duplicate listeners
    selectAllCheckbox.addEventListener("change", handleSelectAllChange);
  }

  const controlsSection = document.querySelector(
    "section.controls-panel .d-flex"
  );
  let toggleSelectAllButton = document.getElementById("toggleSelectAllButton");

  if (controlsSection && !toggleSelectAllButton) {
    toggleSelectAllButton = document.createElement("button");
    toggleSelectAllButton.id = "toggleSelectAllButton";
    toggleSelectAllButton.className = "btn btn-outline-secondary btn-sm";
    toggleSelectAllButton.innerHTML =
      '<i class="fas fa-check-double me-1"></i>Toggle All Visible';
    toggleSelectAllButton.title =
      "Toggle selection of all currently visible items";

    const exportAllButton = document.getElementById("exportAll");
    if (exportAllButton && exportAllButton.parentNode === controlsSection) {
      controlsSection.insertBefore(
        toggleSelectAllButton,
        exportAllButton.nextSibling
      );
    } else if (controlsSection.firstChild) {
      // Insert before the first button if exportAll not found
      controlsSection.insertBefore(
        toggleSelectAllButton,
        controlsSection.firstChild
      );
    } else {
      controlsSection.appendChild(toggleSelectAllButton);
    }
  }

  if (toggleSelectAllButton) {
    toggleSelectAllButton.removeEventListener("click", handleToggleAllClick);
    toggleSelectAllButton.addEventListener("click", handleToggleAllClick);
  }
}
// Helper handler for toggle all button click
function handleToggleAllClick() {
  const selectAllCheckbox = document.getElementById("selectAllCheckbox");
  if (selectAllCheckbox) {
    selectAllCheckbox.checked = !selectAllCheckbox.checked;
    selectAllCheckbox.dispatchEvent(new Event("change")); // Trigger change handler
  } else {
    // Fallback if checkbox somehow doesn't exist
    const userCheckboxes = document.querySelectorAll(
      "#userTable tbody .user-select"
    );
    const allChecked = Array.from(userCheckboxes).every((cb) => cb.checked);
    userCheckboxes.forEach((checkbox) => {
      const row = checkbox.closest("tr");
      if (row && row.style.display !== "none") {
        checkbox.checked = !allChecked;
      } else if (!row || row.style.display === "") {
        checkbox.checked = !allChecked;
      }
    });
  }
}

// Helper function to format date (ensure it exists)
function formatDate(dateString) {
  if (!dateString) return "";
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      // Try parsing specific format if needed, e.g., MySQL 'YYYY-MM-DD HH:MM:SS'
      if (
        typeof dateString === "string" &&
        /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateString)
      ) {
        const parts = dateString.split(/[- :]/);
        // Note: JS months are 0-indexed
        const isoDate = new Date(
          Date.UTC(
            parts[0],
            parts[1] - 1,
            parts[2],
            parts[3],
            parts[4],
            parts[5]
          )
        );
        if (!isNaN(isoDate.getTime()))
          return isoDate.toLocaleString("de-DE", { timeZone: "Europe/Berlin" }); // Adjust timezone if needed
      }
      return dateString; // Return original if parsing fails
    }
    return date.toLocaleString("de-DE", { timeZone: "Europe/Berlin" }); // Use locale string with timezone
  } catch (error) {
    console.error("Error formatting date:", dateString, error);
    return "Invalid Date";
  }
}
