<?php
// Ensure this is at the top of your PHP files for better error handling
ini_set('display_errors', 0); // Don't display errors to the user
ini_set('log_errors', 1);     // Log errors to a file
error_reporting(E_ALL);

header('Content-Type: application/json'); // Crucial: Set the content type to JSON

require_once __DIR__ . '/db_connect.php';

// --- Main Logic ---
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $conn->begin_transaction(); // Start a transaction for safety

    try {
        $code = isset($_POST['code']) ? mb_strtoupper(trim($_POST['code']), 'UTF-8') : '';

        if (empty($code)) {
            throw new Exception("Code cannot be empty.");
        }

        // Check if the code already exists
        $check_query = "SELECT id FROM users WHERE user_code = ?";
        $check_stmt = $conn->prepare($check_query);
        if (!$check_stmt) {
            throw new Exception("Database prepare statement failed (check): " . $conn->error);
        }
        $check_stmt->bind_param("s", $code);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            // Code already exists, send a specific JSON response
            echo json_encode(["ok" => false, "isDuplicateCode" => true, "message" => "Code already exists"]);
            $check_stmt->close();
            $conn->rollback(); // Rollback the transaction
            $conn->close();
            exit();
        }
        $check_stmt->close();

        // Code is unique, proceed with registration
        $insert_query = "INSERT INTO users (user_code, attempt_number) VALUES (?, 1)";
        $insert_stmt = $conn->prepare($insert_query);
        if (!$insert_stmt) {
            throw new Exception("Database prepare statement failed (insert): " . $conn->error);
        }
        $insert_stmt->bind_param("s", $code);

        if (!$insert_stmt->execute()) {
            throw new Exception("Database execute failed (insert): " . $insert_stmt->error);
        }
        
        // Get the auto-generated ID
        $userId = $insert_stmt->insert_id;
        $insert_stmt->close();

        $conn->commit(); // Commit the transaction

        // Return success and the user ID
        echo json_encode(["ok" => true, "userId" => $userId, "message" => "Registration successful"]);

    } catch (Exception $e) {
        $conn->rollback(); // Rollback on any error
        // Log the detailed error for your own debugging
        error_log("Registration Error: " . $e->getMessage());

        // Send a generic, safe error message back to the client
        http_response_code(500); // Internal Server Error
        echo json_encode(["ok" => false, "message" => "Error registering user. Please try again."]);

    } finally {
        // Ensure the connection is closed
        if (isset($conn)) {
            $conn->close();
        }
    }

} else {
    // Not a POST request
    http_response_code(405); // Method Not Allowed
    echo json_encode(["ok" => false, "message" => "Invalid request method"]);
}
?>